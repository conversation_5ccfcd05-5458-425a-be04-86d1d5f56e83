import sys
sys.path.append("..")

import cv2
import numpy as np
import pupil_apriltags as apriltag
from uptech import UpTech
import time
import random

class RobotFighter:
    def __init__(self):
        # 初始化硬件
        self.uptech = UpTech()
        self.uptech.ADC_IO_Open()
        self.uptech.CDS_Open()
        self.uptech.Camera_Open()
        
        # 初始化AprilTag检测器
        self.detector = apriltag.Detector(families='tag36h11')
        
        # 配置舵机
        # 左右轮
        self.uptech.CDS_SetMode(1, 0)  # 左轮
        self.uptech.CDS_SetMode(2, 0)  # 右轮
        
        # 左胳膊的3个舵机编号分别为4、5、6
        self.uptech.CDS_SetMode(4, 0)
        self.uptech.CDS_SetMode(5, 0)
        self.uptech.CDS_SetMode(6, 0)
        
        # 右胳膊的3个舵机编号分别为7、8、9
        self.uptech.CDS_SetMode(7, 0)  
        self.uptech.CDS_SetMode(8, 0)     
        self.uptech.CDS_SetMode(9, 0)
        
        # 状态变量
        self.forward_fall_count = 0  # 前倾计数
        self.backward_fall_count = 0  # 后倾计数
        self.search_count = 0  # 搜索计数
        self.recovery_attempts = 0  # 恢复尝试次数
        self.last_attack_time = time.time()  # 上次攻击时间
        
        # 倾角传感器阈值
        self.angle_normal_min = 1428
        self.angle_normal_max = 2628
        
        # 初始化完成后，执行默认姿势
        self.default_pose()
        print("机器人初始化完成")
    
    #---------------------------传感器相关函数---------------------------
    
    def get_tilt_angle(self):
        """
        获取倾角传感器值
        返回值:
        1 - 正常站立
        2 - 前倾
        3 - 后倾
        0 - 不确定状态
        """
        angle_value = self.uptech.ADC_Get_Channel(2)  # 使用通道2作为倾角传感器
        print(f"倾角传感器值: {angle_value}")
        
        # 正常站立
        if self.angle_normal_min < angle_value < self.angle_normal_max:
            return 1
        # 前倾
        elif angle_value <= self.angle_normal_min:
            return 2
        # 后倾
        elif angle_value >= self.angle_normal_max:
            return 3
        # 不确定状态
        else:
            return 0
    
    def get_gray_sensors(self):
        """获取灰度传感器值"""
        front_gray = self.uptech.ADC_Get_Channel(0)  # 前灰度传感器
        back_gray = self.uptech.ADC_Get_Channel(1)   # 后灰度传感器
        return front_gray, back_gray
    
    def detect_apriltags(self):
        """检测画面中的AprilTag标签"""
        try:
            frame = self.uptech.Camera_Get_Image()
            if frame is None:
                print("获取摄像头图像失败")
                return None
                
            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 检测AprilTag
            results = self.detector.detect(gray)
            
            if not results:
                return None
                
            # 返回检测到的标签信息
            detected_tags = []
            for r in results:
                tag_id = r.tag_id
                center = (int(r.center[0]), int(r.center[1]))
                corners = r.corners
                # 计算标签大小作为距离的估计
                size = np.mean([
                    np.linalg.norm(corners[0] - corners[1]),
                    np.linalg.norm(corners[1] - corners[2]),
                    np.linalg.norm(corners[2] - corners[3]),
                    np.linalg.norm(corners[3] - corners[0])
                ])
                
                detected_tags.append({
                    'id': tag_id,
                    'center': center,
                    'size': size,
                    'frame_width': frame.shape[1],
                    'frame_height': frame.shape[0]
                })
                
            return detected_tags
        except Exception as e:
            print(f"AprilTag检测错误: {e}")
            return None
    
    def get_enemy_position(self):
        """
        获取敌人位置信息
        返回值:
        (0, None) - 未检测到敌人
        (tag_id, position) - 敌人ID和相对位置
            tag_id: 1-前方, 2-左侧, 3-右侧, 4-后方
            position: 相对位置 (x, y, size)，x和y是标签中心在图像中的位置，size是标签大小
        """
        tags = self.detect_apriltags()
        if not tags:
            return (0, None)
            
        # 如果检测到多个标签，选择最大的一个（最近的）
        largest_tag = max(tags, key=lambda x: x['size'])
        tag_id = largest_tag['id']
        
        # 计算标签在图像中的相对位置
        frame_width = largest_tag['frame_width']
        frame_height = largest_tag['frame_height']
        center_x = largest_tag['center'][0] / frame_width  # 归一化到0-1
        center_y = largest_tag['center'][1] / frame_height  # 归一化到0-1
        
        return (tag_id, (center_x, center_y, largest_tag['size']))
    
    #---------------------------运动控制函数---------------------------
    
    def move(self, left_speed, right_speed, duration=0):
        """
        控制机器人移动
        left_speed: 左轮速度
        right_speed: 右轮速度
        duration: 持续时间，为0时不等待
        """
        self.uptech.CDS_SetSpeed(1, left_speed)
        self.uptech.CDS_SetSpeed(2, -right_speed)
        
        if duration > 0:
            time.sleep(duration)
            self.stop()
    
    def stop(self):
        """停止移动"""
        self.uptech.CDS_SetSpeed(1, 0)
        self.uptech.CDS_SetSpeed(2, 0)
    
    def default_pose(self):
        """默认站立姿势"""
        self.uptech.CDS_SetAngle(4, 512, 512)  # 左肩
        self.uptech.CDS_SetAngle(5, 512, 512)  # 左肘
        self.uptech.CDS_SetAngle(6, 512, 512)  # 左腕
        self.uptech.CDS_SetAngle(7, 512, 512)  # 右肩
        self.uptech.CDS_SetAngle(8, 512, 512)  # 右肘
        self.uptech.CDS_SetAngle(9, 512, 512)  # 右腕
        time.sleep(0.5)
    
    def start_pose(self):
        """上台前的准备姿势"""
        # 摆个Pose
        self.uptech.CDS_SetAngle(4, 700, 512)  # 左肩前伸 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 400, 512)  # 左肘向上 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(6, 400, 512)  # 左腕向上 (6号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 300, 512)  # 右肩前伸 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 400, 512)  # 右肘向上 (8号数值减小=向上)
        self.uptech.CDS_SetAngle(9, 400, 512)  # 右腕向上 (9号数值减小=向上)
        time.sleep(0.3)

    def stand_up_forward(self):
        """前倾倒后站起"""
        # 第一步：双臂前伸
        self.uptech.CDS_SetAngle(4, 700, 512)  # 左肩前伸 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 600, 512)  # 左肘向下支撑 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(6, 600, 512)  # 左腕向下支撑 (6号数值增大=向下)
        self.uptech.CDS_SetAngle(7, 300, 512)  # 右肩前伸 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 600, 512)  # 右肘向下支撑 (8号数值增大=向下)
        self.uptech.CDS_SetAngle(9, 600, 512)  # 右腕向下支撑 (9号数值增大=向下)
        time.sleep(1.0)

        # 第二步：支撑身体
        self.uptech.CDS_SetAngle(4, 800, 800)  # 左肩更前伸用力 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(7, 200, 800)  # 右肩更前伸用力 (7号数值减小=向前)
        time.sleep(1.0)

        # 第三步：抬起上身
        self.uptech.CDS_SetAngle(4, 350, 512)  # 左肩稍微后收
        self.uptech.CDS_SetAngle(5, 450, 512)  # 左肘向上 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 350, 512)  # 右肩稍微后收
        self.uptech.CDS_SetAngle(8, 450, 512)  # 右肘向上 (8号数值减小=向上)
        time.sleep(0.8)

        # 第四步：恢复站立
        self.default_pose()

        # 向后移动一点以稳定身体
        self.move(-300, -300, 0.3)

    def stand_up_backward(self):
        """后倾倒后站起"""
        # 第一步：双臂后伸
        self.uptech.CDS_SetAngle(4, 300, 512)  # 左肩后伸 (4号数值减小=向后)
        self.uptech.CDS_SetAngle(5, 600, 512)  # 左肘向下支撑 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(6, 600, 512)  # 左腕向下支撑 (6号数值增大=向下)
        self.uptech.CDS_SetAngle(7, 700, 512)  # 右肩后伸 (7号数值增大=向后)
        self.uptech.CDS_SetAngle(8, 600, 512)  # 右肘向下支撑 (8号数值增大=向下)
        self.uptech.CDS_SetAngle(9, 600, 512)  # 右腕向下支撑 (9号数值增大=向下)
        time.sleep(1.0)

        # 第二步：支撑身体
        self.uptech.CDS_SetAngle(4, 200, 800)  # 左肩更后伸用力 (4号数值减小=向后)
        self.uptech.CDS_SetAngle(7, 800, 800)  # 右肩更后伸用力 (7号数值增大=向后)
        time.sleep(1.0)

        # 第三步：抬起上身
        self.uptech.CDS_SetAngle(4, 650, 512)  # 左肩稍微前收
        self.uptech.CDS_SetAngle(5, 450, 512)  # 左肘向上 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 650, 512)  # 右肩稍微前收
        self.uptech.CDS_SetAngle(8, 450, 512)  # 右肘向上 (8号数值减小=向上)
        time.sleep(0.8)

        # 第四步：恢复站立
        self.default_pose()

        # 向前移动一点以稳定身体
        self.move(300, 300, 0.3)

    def attack_front(self):
        """正面攻击动作"""
        # 准备姿势
        self.uptech.CDS_SetAngle(4, 600, 512)  # 左肩微前 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 450, 512)  # 左肘向上准备 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(6, 450, 512)  # 左腕向上准备 (6号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 400, 512)  # 右肩微前 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 450, 512)  # 右肘向上准备 (8号数值减小=向上)
        self.uptech.CDS_SetAngle(9, 450, 512)  # 右腕向上准备 (9号数值减小=向上)
        time.sleep(0.3)

        # 快速推出
        self.uptech.CDS_SetAngle(4, 750, 1000)  # 左肩快速前伸 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 600, 1000)  # 左肘向下推出 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(7, 250, 1000)  # 右肩快速前伸 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 600, 1000)  # 右肘向下推出 (8号数值增大=向下)

        # 同时向前冲
        self.move(500, 500, 0.6)

        # 恢复默认姿势
        self.default_pose()

        # 更新上次攻击时间
        self.last_attack_time = time.time()

    def attack_left(self):
        """左侧攻击动作"""
        # 左转
        self.move(-300, 300, 0.4)

        # 左臂攻击
        self.uptech.CDS_SetAngle(4, 750, 800)  # 左肩前伸 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 600, 800)  # 左肘向下推出 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(6, 600, 512)  # 左腕向下推出 (6号数值增大=向下)
        time.sleep(0.3)

        # 向左前方冲
        self.move(400, 300, 0.5)

        # 恢复默认姿势
        self.default_pose()

        # 更新上次攻击时间
        self.last_attack_time = time.time()

    def attack_right(self):
        """右侧攻击动作"""
        # 右转
        self.move(300, -300, 0.4)

        # 右臂攻击
        self.uptech.CDS_SetAngle(7, 250, 800)  # 右肩前伸 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 600, 800)  # 右肘向下推出 (8号数值增大=向下)
        self.uptech.CDS_SetAngle(9, 600, 512)  # 右腕向下推出 (9号数值增大=向下)
        time.sleep(0.3)

        # 向右前方冲
        self.move(300, 400, 0.5)

        # 恢复默认姿势
        self.default_pose()

        # 更新上次攻击时间
        self.last_attack_time = time.time()

    def attack_back(self):
        """后方攻击动作"""
        # 转身180度
        self.move(400, -400, 1.0)

        # 执行前方攻击
        self.attack_front()

    def search_rotate(self, direction=1):
        """
        搜索旋转
        direction: 1为顺时针，-1为逆时针
        """
        speed = 300
        self.move(speed * direction, -speed * direction, 0.05)
    
    def stand_up_forward(self):
        """前倾倒后站起"""
        # 第一步：双臂前伸
        self.uptech.CDS_SetAngle(4, 700, 512)  # 左肩前伸 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 600, 512)  # 左肘向下支撑 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(6, 600, 512)  # 左腕向下支撑 (6号数值增大=向下)
        self.uptech.CDS_SetAngle(7, 300, 512)  # 右肩前伸 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 600, 512)  # 右肘向下支撑 (8号数值增大=向下)
        self.uptech.CDS_SetAngle(9, 600, 512)  # 右腕向下支撑 (9号数值增大=向下)
        time.sleep(1.0)

        # 第二步：支撑身体
        self.uptech.CDS_SetAngle(4, 800, 800)  # 左肩更前伸用力 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(7, 200, 800)  # 右肩更前伸用力 (7号数值减小=向前)
        time.sleep(1.0)

        # 第三步：抬起上身
        self.uptech.CDS_SetAngle(4, 350, 512)  # 左肩稍微后收
        self.uptech.CDS_SetAngle(5, 450, 512)  # 左肘向上 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 350, 512)  # 右肩稍微后收
        self.uptech.CDS_SetAngle(8, 450, 512)  # 右肘向上 (8号数值减小=向上)
        time.sleep(0.8)

        # 第四步：恢复站立
        self.default_pose()

        # 向后移动一点以稳定身体
        self.move(-300, -300, 0.3)
    
    def stand_up_backward(self):
        """后倾倒后站起"""
        # 第一步：双臂后伸
        self.uptech.CDS_SetAngle(4, 300, 512)  # 左肩后伸 (4号数值减小=向后)
        self.uptech.CDS_SetAngle(5, 600, 512)  # 左肘向下支撑 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(6, 600, 512)  # 左腕向下支撑 (6号数值增大=向下)
        self.uptech.CDS_SetAngle(7, 700, 512)  # 右肩后伸 (7号数值增大=向后)
        self.uptech.CDS_SetAngle(8, 600, 512)  # 右肘向下支撑 (8号数值增大=向下)
        self.uptech.CDS_SetAngle(9, 600, 512)  # 右腕向下支撑 (9号数值增大=向下)
        time.sleep(1.0)

        # 第二步：支撑身体
        self.uptech.CDS_SetAngle(4, 200, 800)  # 左肩更后伸用力 (4号数值减小=向后)
        self.uptech.CDS_SetAngle(7, 800, 800)  # 右肩更后伸用力 (7号数值增大=向后)
        time.sleep(1.0)

        # 第三步：抬起上身
        self.uptech.CDS_SetAngle(4, 650, 512)  # 左肩稍微前收
        self.uptech.CDS_SetAngle(5, 450, 512)  # 左肘向上 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 650, 512)  # 右肩稍微前收
        self.uptech.CDS_SetAngle(8, 450, 512)  # 右肘向上 (8号数值减小=向上)
        time.sleep(0.8)

        # 第四步：恢复站立
        self.default_pose()

        # 向前移动一点以稳定身体
        self.move(300, 300, 0.3)
    
    def attack_front(self):
        """正面攻击动作"""
        # 准备姿势
        self.uptech.CDS_SetAngle(4, 600, 512)  # 左肩微前 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 450, 512)  # 左肘向上准备 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(6, 450, 512)  # 左腕向上准备 (6号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 400, 512)  # 右肩微前 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 450, 512)  # 右肘向上准备 (8号数值减小=向上)
        self.uptech.CDS_SetAngle(9, 450, 512)  # 右腕向上准备 (9号数值减小=向上)
        time.sleep(0.3)

        # 快速推出
        self.uptech.CDS_SetAngle(4, 750, 1000)  # 左肩快速前伸 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 600, 1000)  # 左肘向下推出 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(7, 250, 1000)  # 右肩快速前伸 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 600, 1000)  # 右肘向下推出 (8号数值增大=向下)

        # 同时向前冲
        self.move(500, 500, 0.6)

        # 恢复默认姿势
        self.default_pose()

        # 更新上次攻击时间
        self.last_attack_time = time.time()
    
    def attack_left(self):
        """左侧攻击动作"""
        # 左转
        self.move(-300, 300, 0.4)

        # 左臂攻击
        self.uptech.CDS_SetAngle(4, 750, 800)  # 左肩前伸 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 600, 800)  # 左肘向下推出 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(6, 600, 512)  # 左腕向下推出 (6号数值增大=向下)
        time.sleep(0.3)

        # 向左前方冲
        self.move(400, 300, 0.5)

        # 恢复默认姿势
        self.default_pose()

        # 更新上次攻击时间
        self.last_attack_time = time.time()
    
    def attack_right(self):
        """右侧攻击动作"""
        # 右转
        self.move(300, -300, 0.4)

        # 右臂攻击
        self.uptech.CDS_SetAngle(7, 250, 800)  # 右肩前伸 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 600, 800)  # 右肘向下推出 (8号数值增大=向下)
        self.uptech.CDS_SetAngle(9, 600, 512)  # 右腕向下推出 (9号数值增大=向下)
        time.sleep(0.3)

        # 向右前方冲
        self.move(300, 400, 0.5)

        # 恢复默认姿势
        self.default_pose()

        # 更新上次攻击时间
        self.last_attack_time = time.time()
    
    def attack_back(self):
        """后方攻击动作"""
        # 转身180度
        self.move(400, -400, 1.0)
        
        # 执行前方攻击
        self.attack_front()
    
    def search_rotate(self, direction=1):
        """
        搜索旋转
        direction: 1为顺时针，-1为逆时针
        """
        speed = 300
        self.move(speed * direction, -speed * direction, 0.05)
    
    #---------------------------行为处理函数---------------------------
    
    def handle_falling(self, tilt_state):
        """处理倒下状态"""
        # 前倾
        if tilt_state == 2:
            self.forward_fall_count += 1
            if self.forward_fall_count >= 5:
                print("检测到前倾倒，尝试恢复站立")
                self.stop()
                time.sleep(0.1)
                
                # 执行前倾恢复动作
                self.stand_up_forward()
                time.sleep(0.5)
                
                # 检查是否恢复成功
                if self.get_tilt_angle() == 1:
                    print("成功恢复站立")
                    self.forward_fall_count = 0
                    self.recovery_attempts = 0
                else:
                    print("恢复失败，再次尝试")
                    self.recovery_attempts += 1
                    # 如果多次尝试失败，尝试不同的策略
                    if self.recovery_attempts > 3:
                        print("多次恢复失败，尝试其他策略")
                        self.move(200, 200, 0.3)
                        self.recovery_attempts = 0
            else:
                time.sleep(0.05)
        
        # 后倾
        elif tilt_state == 3:
            self.backward_fall_count += 1
            if self.backward_fall_count >= 5:
                print("检测到后倾倒，尝试恢复站立")
                self.stop()
                time.sleep(0.1)
                
                # 执行后倾恢复动作
                self.stand_up_backward()
                time.sleep(0.5)
                
                # 检查是否恢复成功
                if self.get_tilt_angle() == 1:
                    print("成功恢复站立")
                    self.backward_fall_count = 0
                    self.recovery_attempts = 0
                else:
                    print("恢复失败，再次尝试")
                    self.recovery_attempts += 1
                    # 如果多次尝试失败，尝试不同的策略
                    if self.recovery_attempts > 3:
                        print("多次恢复失败，尝试其他策略")
                        self.move(-200, -200, 0.3)
                        self.recovery_attempts = 0
            else:
                time.sleep(0.05)
        
        # 不确定状态
        else:
            print("倾倒状态不明确，尝试轻微移动")
            self.move(150, 150, 0.2)
    
    def handle_enemy(self, enemy_info):
        """处理敌人信息"""
        tag_id, position = enemy_info
        
        # 攻击冷却时间检查 - 防止连续攻击
        current_time = time.time()
        if current_time - self.last_attack_time < 2.0:  # 2秒冷却时间
            self.search_rotate()
            return
        
        # 未检测到敌人
        if tag_id == 0:
            self.search_enemy()
            return
        
        # 根据标签ID确定敌人方向
        if tag_id == 1:  # 前方
            print("敌人在前方，发起攻击")
            # 如果有位置信息，根据位置微调方向
            if position:
                x, y, size = position
                if x < 0.4:  # 敌人偏左
                    self.move(-150, 150, 0.1)  # 微调左转
                elif x > 0.6:  # 敌人偏右
                    self.move(150, -150, 0.1)  # 微调右转
            
            # 攻击
            self.attack_front()
            
        elif tag_id == 2:  # 左侧
            print("敌人在左侧，左转攻击")
            self.attack_left()
            
        elif tag_id == 3:  # 右侧
            print("敌人在右侧，右转攻击")
            self.attack_right()
            
        elif tag_id == 4:  # 后方
            print("敌人在后方，转身攻击")
            self.attack_back()
    
    def search_enemy(self):
        """搜索敌人"""
        self.search_count += 1
        
        # 每20次循环改变一次搜索策略
        if self.search_count >= 20:
            self.search_count = 0
            strategy = random.randint(1, 4)
            
            if strategy == 1:
                # 原地旋转
                print("搜索策略：原地旋转")
                turn_dir = random.choice([-1, 1])
                self.move(300 * turn_dir, -300 * turn_dir, 0.8)
                
            elif strategy == 2:
                # 前进一段距离
                print("搜索策略：前进")
                self.move(300, 300, 0.6)
                
            elif strategy == 3:
                # 弧线移动
                print("搜索策略：弧线移动")
                self.move(350, 250, 0.7)
                
            else:
                # 随机转向
                print("搜索策略：随机转向")
                turn_angle = random.uniform(0.3, 1.0)
                turn_dir = random.choice([-1, 1])
                self.move(300 * turn_dir, -300 * turn_dir, turn_angle)
                
            # 恢复默认姿势
            self.default_pose()
            
        else:
            # 缓慢旋转寻找敌人
            self.search_rotate()
    
    #---------------------------主循环---------------------------
    
    def run(self):
        """主循环"""
        try:
            print("机器人开始运行...")
            
            # 开始姿势
            self.start_pose()
            time.sleep(1.0)
            print("等待上台...")
            
            # 上台动作
            self.move(300, 300, 2.0)
            self.default_pose()
            time.sleep(0.5)
            
            print("开始比赛...")
            
            while True:
                # 1. 检测倾角状态
                tilt_state = self.get_tilt_angle()
                
                # 2. 如果倒下，执行恢复动作
                if tilt_state != 1:
                    self.handle_falling(tilt_state)
                    continue
                
                # 3. 检测敌人位置
                enemy_info = self.get_enemy_position()
                self.handle_enemy(enemy_info)
                
                # 短暂休眠以节省资源
                time.sleep(0.05)
                
        except KeyboardInterrupt:
            print("程序被用户中断")
            self.stop()
            self.default_pose()
        except Exception as e:
            print(f"程序发生错误: {e}")
            self.stop()
            self.default_pose()

# 主程序入口
if __name__ == "__main__":
    print("仿人散打机器人启动中...")
    robot = RobotFighter()
    
    try:
        robot.run()
    except Exception as e:
        print(f"程序发生错误: {e}")
    finally:
        print("程序结束")
